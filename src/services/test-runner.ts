import { WebSocket } from 'ws';
import { exec, ChildProcess } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';
import { TestEnvironmentService } from './test-environment';


function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function formatTestOutput(output: string): string {
  // Remove all ANSI escape sequences and control characters
  output = output
    // Remove ANSI color codes (e.g., \x1B[38;5;45;1m, \x1B[0m)
    .replace(/\x1B\[[0-9;]*m/g, '')
    // Remove cursor movement sequences (e.g., [1A[2K)
    .replace(/\[1A\[2K/g, '')
    // Remove other ANSI escape sequences
    .replace(/\x1B\[[0-9;]*[A-Za-z]/g, '')
    // Remove control characters
    .replace(/[\x00-\x1F\x7F]/g, '')
    // Remove [0-0] worker prefix (not useful for users)
    .replace(/^\[0-0\]\s+/g, '')
    .replace(/\[0-0\]\s+/g, ' ')
    // Clean up extra whitespace
    .trim();

  // Skip empty lines after cleaning
  if (!output) {
    return '';
  }

  // ========== FILTER OUT TECHNICAL/INTERNAL LOGS ==========

  // Skip internal file paths (too technical for end users)
  if (output.includes('/Users/') ||
      output.includes('/home/') ||
      output.includes('client-apps/session-') ||
      output.includes('websocket_ai_mobile_single_test')) {
    // Exception: Keep important success/error messages even if they have paths
    if (!output.includes('✅ iOS: App download completed') &&
        !output.includes('✅ Android: App download completed') &&
        !output.includes('❌') &&
        !output.includes('ERROR')) {
      return '';
    }
    // Clean up the path from the message
    output = output.replace(/\/Users\/[^\s]+\/client-apps\/session-[^\/]+\//g, '');
    output = output.replace(/\/home\/[^\s]+\/client-apps\/session-[^\/]+\//g, '');
  }

  // Skip internal session/client IDs (not useful for users)
  if ((output.includes('session-') || output.includes('client-')) &&
      !output.includes('Session ID:') &&
      !output.includes('Client ID:')) {
    return '';
  }

  // Skip GCS/storage URLs (too technical)
  if (output.includes('gs://') && !output.includes('⚠️') && !output.includes('📥')) {
    return '';
  }

  // Skip worker/internal process messages
  if (output.includes('Worker 0-0') ||
      output.includes('Execution of 1 workers') ||
      output.includes('workers started')) {
    return '';
  }

  // Skip duplicate download messages (keep only the final success)
  if (output.includes('📦 ZIP file detected') ||
      output.includes('🔐 Attempting authenticated') ||
      output.includes('📥 Downloading app for client') ||
      output.includes('✅ Downloaded app via authenticated') ||
      output.includes('✅ Found extracted app file')) {
    return '';
  }

  // Skip WebdriverIO internal errors that don't affect test results
  if (output.includes('Method has not yet been implemented') ||
      output.includes('current_activity') ||
      output.includes('Could not get current activity')) {
    return '';
  }

  // Skip stack traces and technical error details (keep only the main error message)
  if (output.includes('at async') ||
      output.includes('at process.') ||
      output.includes('node_modules') ||
      output.includes('webdriver/build') ||
      output.includes('@wdio/') ||
      output.includes('file:///') ||
      output.includes('FetchRequest') ||
      output.includes('dispatcher:') ||
      output.includes('Symbol(') ||
      output.includes('_events:') ||
      output.includes('opts: {') ||
      output.includes('headers: Headers')) {
    return '';
  }

  // Skip verbose debug logs
  if (output.includes('pw:api') ||
      output.includes('To open last HTML report run:') ||
      output.includes('npx wdio') ||
      output.includes('webdriverio') ||
      output.includes('Running 1 test using 1 worker') ||
      output.includes('Default Test Name') ||
      output.includes('fonts loaded') ||
      output.includes('waiting for fonts to load') ||
      output.includes('taking page screenshot') ||
      output.includes('navigations have finished') ||
      output.includes('waiting for scheduled navigations') ||
      output.includes('done scrolling') ||
      output.includes('scrolling into view') ||
      output.includes('performing click action') ||
      output.includes('click action done') ||
      output.includes('attempting') ||
      output.includes('waiting for element to be') ||
      output.includes('element is visible, enabled') ||
      output.includes('locator resolved to') ||
      output.includes('Authenticating with:') ||
      output.includes('auth/login') ||
      output.includes('Recording completion_input') ||
      output.includes('Recording completion_output') ||
      output.includes('Successfully recorded') ||
      output.includes('Connected to WebSocket server') ||
      output.includes('Connecting to WebSocket server') ||
      output.includes('Test completed successfully') ||
      output.includes('waiting for locator')) {
    return '';
  }

  // Skip internal capability/setup details (too technical)
  if (output.includes('Found setup action, configuring') ||
      output.includes('Platform: ios, Device:') ||
      output.includes('Platform: android, Device:') ||
      output.includes('DeviceID:') ||
      output.includes('UDID:')) {
    return '';
  }

  // Skip duplicate "Starting app download" messages
  if (output.includes('📥 iOS: Starting app download') ||
      output.includes('📥 Android: Starting app download') ||
      output.includes('⚠️ iOS: GCS URL detected') ||
      output.includes('⚠️ Android: GCS URL detected') ||
      output.includes('⚠️ GCS URL detected')) {
    return '';
  }

  // Skip internal test framework messages
  if (output.includes('RUNNING in iOS') ||
      output.includes('RUNNING in Android') ||
      output.includes('file:///tests/master.spec.ts') ||
      output.includes('Setting up global test environment') ||
      output.includes('Starting suite:') ||
      output.includes('Starting test: should execute') ||
      output.includes('Suite completed:') ||
      output.includes('All tests completed') ||
      output.includes('Session terminated') ||
      output.includes('Worker 0-0 ended') ||
      output.includes('"DotReporter" Reporter')) {
    return '';
  }

  // Skip verbose action details (keep only the main action message)
  if (output.includes('🔍 Action details: {') ||
      output.includes('🔍 Getting element with selector') ||
      output.includes('🔍 Element object created') ||
      output.includes('⏳ Waiting for element to exist')) {
    return '';
  }

  // Skip error object properties (technical details from WebDriver errors)
  if (output.includes('origin:') ||
      output.includes('protocol:') ||
      output.includes('hostname:') ||
      output.includes('pathname:') ||
      output.includes('searchParams:') ||
      output.includes('URLSearchParams') ||
      output.includes('hash:') ||
      output.includes('username:') ||
      output.includes('password:') ||
      output.includes('host:') ||
      output.includes('port:') ||
      output.includes('search:') ||
      output.includes('Connection:') ||
      output.includes('User-Agent:') ||
      output.includes('Content-Type:') ||
      output.includes("'Content-Type'") ||
      output.includes('"Content-Type"') ||
      output.includes('Accept:') ||
      output.includes("'Accept'") ||
      output.includes('"Accept"') ||
      output.includes("'Connection'") ||
      output.includes('"Connection"') ||
      output.includes("'User-Agent'") ||
      output.includes('"User-Agent"') ||
      output.includes('_events:') ||
      output.includes('_eventsCount:') ||
      output.includes('_maxListeners:') ||
      output.includes('dispatcher:') ||
      output.includes('Symbol(') ||
      output.includes('headers: Headers') ||
      output.includes('opts: {') ||
      output.includes('url: URL {') ||
      output.includes('href:') ||
      output.includes('method:') ||
      output.includes('redirect:') ||
      output.includes('signal:') ||
      output.includes('AbortSignal')) {
    return '';
  }

  // Skip closing braces and brackets from error objects
  if (output.trim() === '}' ||
      output.trim() === '},' ||
      output.trim() === ']' ||
      output.trim() === '],' ||
      output.trim() === '.' ||
      output.trim() === '} }' ||
      output.trim() === '} } }' ||
      output.trim().match(/^\}\s*\}$/) ||
      output.trim().match(/^\}\s*\}\s*\}$/) ||
      output.trim().match(/^\[0-0\]\s*\}$/) ||
      output.trim().match(/^\[0-0\]\s*\}\[0-0\]\s*\}$/) ||
      output.trim().match(/^\[0-0\]\s*\}\[0-0\]\s*\}\[0-0\]\s*\}$/)) {
    return '';
  }

  // Skip lines that look like object property dumps (key: value format with technical content)
  // This includes both unquoted (key:) and quoted ('key':) property names
  // BUT: Always preserve messages with emojis or "Step" keyword first
  if (!output.includes('Step') &&
      !output.includes('✅') &&
      !output.includes('❌') &&
      !output.includes('⚠️') &&
      !output.includes('🔍') &&
      !output.includes('📱') &&
      !output.includes('⌨️') &&
      !output.includes('🖱️') &&
      !output.includes('🌐')) {
    // Only filter if it looks like a property dump AND doesn't have important markers
    if (output.match(/^\[0-0\]\s+[a-zA-Z_]+:\s+/) ||
        output.match(/^\s+[a-zA-Z_]+:\s+['"]/) ||
        output.match(/^\s+[a-zA-Z_]+:\s+\{/) ||
        output.match(/^\s+[a-zA-Z_]+:\s+\[/) ||
        output.match(/^\s*['"][a-zA-Z_-]+['"]\s*:\s+/) ||
        output.match(/^\[0-0\]\s+['"][a-zA-Z_-]+['"]\s*:\s+/)) {
      return '';
    }
  }

  // Skip "All tests completed" message (redundant)
  if (output.includes('All` tests completed') ||
      output.includes('All tests completed')) {
    return '';
  }

  // ========== USER-FRIENDLY MESSAGE FORMATTING ==========

  // Simplify app download completion messages
  if (output.includes('✅ iOS: App download completed') ||
      output.includes('✅ Android: App download completed')) {
    return '📱 App ready for testing';
  }

  // Simplify test execution start
  if (output.includes('🚀 Starting mobile test execution')) {
    return '🚀 Starting test execution...';
  }

  // Simplify test data loaded
  if (output.includes('📱 Loaded test data with')) {
    const stepsMatch = output.match(/(\d+) steps/);
    if (stepsMatch) {
      return `📋 Test loaded with ${stepsMatch[1]} step(s)`;
    }
    return '📋 Test data loaded';
  }

  // Format test step actions (user-friendly)
  // Keep all step messages with emojis (setup, write, click, etc.)
  if (output.includes('📱 Step') ||
      output.includes('⌨️ Step') ||
      output.includes('🖱️ Step')) {
    // Keep step name messages as-is (they're already user-friendly)
    return output;
  }

  if (output.includes('⌨️ Writing')) {
    // This is the fallback when no stepName is provided
    const valueMatch = output.match(/["']([^"']+)["']/g);
    if (valueMatch && valueMatch.length >= 1) {
      const value = valueMatch[0].replace(/["']/g, '');
      return `⌨️ Entering "${value}"`;
    }
    return output;
  }

  if (output.includes('✅ Successfully completed step')) {
    // Extract step number and show simple success message
    const stepMatch = output.match(/step (\d+)/);
    if (stepMatch) {
      return `✅ Step ${stepMatch[1]} completed`;
    }
    return '✅ Step completed';
  }

  if (output.includes('Successfully wrote')) {
    // Legacy format - hide it, we have better messages now
    return '';
  }

  if (output.includes('🖱️ Step')) {
    // Keep step name messages for click actions as-is
    return output;
  }

  if (output.includes('🖱️ Clicking')) {
    // This is the fallback when no stepName is provided
    const targetMatch = output.match(/["']([^"']+)["']/);
    if (targetMatch) {
      return `🖱️ Clicking element`;
    }
    return output;
  }

  if (output.includes('Successfully clicked')) {
    // Legacy format - hide it, we have better messages now
    return '';
  }

  // Format element found messages
  if (output.includes('✅ Element found')) {
    return ''; // Skip this, it's too verbose
  }

  // Format test completion messages
  if (output.includes('📊 Test completed:')) {
    return ''; // Skip, we have better summary messages
  }

  // Format final test results
  if (output.includes('PASSED in iOS') || output.includes('PASSED in Android')) {
    return '✅ Test passed successfully';
  }

  if (output.includes('FAILED in iOS') || output.includes('FAILED in Android')) {
    return '❌ Test failed';
  }

  // Format spec files summary
  if (output.includes('Spec Files:') && output.includes('passed')) {
    const timeMatch = output.match(/in (\d+:\d+:\d+)/);
    if (timeMatch) {
      return `✅ Test completed in ${timeMatch[1]}`;
    }
    return '✅ Test completed successfully';
  }

  // Format test execution complete
  if (output.includes('✅ Appium/WebdriverIO test execution completed')) {
    return ''; // Skip, redundant with other messages
  }

  // Format exit code
  if (output.includes('Exit code: 0')) {
    return ''; // Skip, redundant
  }

  // Format test passed
  if (output.includes('✅ Test passed successfully')) {
    return output; // Keep as is
  }

  // Format queue/status messages
  if (output.includes('📋 Test queued and will start shortly')) {
    return '⏳ Test queued, waiting to start...';
  }

  if (output.includes('🎯 Found completed test execution')) {
    return ''; // Skip internal polling message
  }

  // Format test step execution
  if (output.includes('===== STARTING TEST STEP EXECUTION =====')) {
    // Skip this message, we already have "📋 Test loaded with X step(s)"
    return '';
  }

  if (output.includes('✅ All test steps completed successfully')) {
    return ''; // Skip, we have better summary
  }

  if (output.includes('🏁 TEST_EXECUTION_COMPLETE')) {
    return ''; // Skip internal message
  }

  if (output.includes('✅ DEVICE_FARM_TEST_STATUS: PASSED')) {
    return ''; // Skip internal status
  }

  // Format session ID (simplify)
  if (output.includes('🔍 Session ID:')) {
    return ''; // Skip session ID, not useful for users
  }

  // Format test steps with emojis
  if (output.includes('Running')) {
    return `🚀 ${output}`;
  }

  if (output.includes('Step: prompt')) {
    return `🌐 ${output}`;
  }

  if (output.includes('Step: goto')) {
    return `🌐 ${output}`;
  }

  if (output.includes('Step: navigate')) {
    return `🌐 ${output}`;
  }

  if (output.includes('Step: write') || output.includes('Step: fill')) {
    return `⌨️ ${output}`;
  }

  if (output.includes('Step: click')) {
    return `🖱️ ${output}`;
  }

  if (output.includes('Step: pause')) {
    return `⏳ ${output}`;
  }

  if (output.includes('Step: upload')) {
    return `📤 ${output}`;
  }

  if (output.includes('Step: assertText')) {
    return `🔎 ${output}`;
  }

  if (output.includes('goto')) {
    return `🌐 ${output}`;
  }

  if (output.includes('visit')) {
    return `🌐 ${output}`;
  }

  if (output.includes('click')) {
    return `🖱️ ${output}`;
  }

  if (output.includes('fill')) {
    return `⌨️ ${output}`;
  }

  if (output.includes('write')) {
    return `⌨️ ${output}`;
  }

  if (output.includes('pause')) {
    return `⏳ ${output}`;
  }

  if (output.includes('upload')) {
    return `📤 ${output}`;
  }

  if (output.includes('expect(')) {
    return `🔎 ${output}`;
  }

  // Format test results
  if (output.includes('passed') || output.includes('✓')) {
    return `✅ ${output}`;
  }

  if (output.includes('failed') || output.includes('✘')) {
    return `❌ ${output}`;
  }

  if (output.includes('Error:')) {
    return `⚠️ ${output}`;
  }

  if (output.includes('Authenticating with:')) {
    return `🔐 ${output}`;
  }

  if (output.includes('navigated to')) {
    return `🌐 ${output}`;
  }

  // Return cleaned output
  return output;
}

export class TestRunnerService {
  // Use clientId as key instead of apiKey to support multiple concurrent connections per API key
  private static activeClients = new Map<string, WebSocket>();
  private static activeProcesses = new Map<string, ChildProcess>();
  private static readonly CONNECTION_TIMEOUT = 60000; // 60 seconds timeout
  private static sentLogMessages = new Set<string>();
  private static activeTests = new Map<string, boolean>();
  private static clientHeartbeats = new Map<string, number>();
  private static readonly HEARTBEAT_INTERVAL = 30000; // 30 seconds
  private static readonly HEARTBEAT_TIMEOUT = 90000; // 90 seconds
  private static lastTestRunTimes = new Map<string, number>();
  private static testLogs = new Map<string, string[]>();
  // Map to track which clientId belongs to which apiKey
  private static clientToApiKey = new Map<string, string>();

  // Start the heartbeat checker
  static startHeartbeatChecker() {
    setInterval(() => {
      const now = Date.now();
      
      // Check each client's last heartbeat
      this.clientHeartbeats.forEach((lastHeartbeat, apiKey) => {
        if (now - lastHeartbeat > this.HEARTBEAT_TIMEOUT) {
          console.log(`Client ${apiKey} heartbeat timeout, removing client`);
          this.removeClient(apiKey);
        }
      });
    }, this.HEARTBEAT_INTERVAL);
  }

  static addClient(apiKey: string, ws: WebSocket, clientId?: string) {
    // Generate unique clientId if not provided
    const uniqueClientId = clientId || `client-${apiKey}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    console.log(`Adding client with ID: ${uniqueClientId} for API key: ${apiKey}`);

    // Store the mapping between clientId and apiKey
    this.clientToApiKey.set(uniqueClientId, apiKey);

    // Set connection timeout
    const connectionTimeout = setTimeout(() => {
      if (ws.readyState !== WebSocket.OPEN) {
        ws.terminate();
        this.removeClientById(uniqueClientId);
        console.log(`Connection timeout for client ${uniqueClientId}`);
      }
    }, this.CONNECTION_TIMEOUT);

    // Clear timeout when connection is established
    ws.once('open', () => {
      clearTimeout(connectionTimeout);
      console.log(`Client ${uniqueClientId} connected successfully`);
    });

    // Set initial heartbeat
    this.clientHeartbeats.set(uniqueClientId, Date.now());

    this.activeClients.set(uniqueClientId, ws);
    
    // Set up ping-pong to keep connection alive
    const pingInterval = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.ping();
      } else {
        clearInterval(pingInterval);
        this.removeClientById(uniqueClientId);
      }
    }, 15000);

    // Handle pong responses
    ws.on('pong', () => {
      // Update heartbeat time
      this.clientHeartbeats.set(uniqueClientId, Date.now());
    });

    // Handle ping messages from client
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message.toString());
        if (data.type === 'ping') {
          // Update heartbeat time
          this.clientHeartbeats.set(uniqueClientId, Date.now());

          // Send pong response
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({ type: 'pong' }));
          }
        }
      } catch (error) {
        // Not a JSON message or not a ping, ignore
      }
    });

    ws.on('error', (error) => {
      console.error(`WebSocket error for client ${uniqueClientId}:`, error);
      clearInterval(pingInterval);
      this.removeClientById(uniqueClientId);
    });

    ws.on('close', () => {
      console.log(`Client ${uniqueClientId} disconnected`);
      clearInterval(pingInterval);
      this.removeClientById(uniqueClientId);

      // Only kill the process if there are no other active clients with this API key
      const hasOtherClients = Array.from(this.clientToApiKey.values()).includes(apiKey);
      if (!hasOtherClients) {
        const process = this.activeProcesses.get(uniqueClientId);
        if (process) {
          process.kill();
          this.activeProcesses.delete(uniqueClientId);
        }
      }
    });

    return uniqueClientId; // Return the unique client ID
  }

  static removeClient(apiKey: string) {
    // Remove all clients with this API key
    const clientsToRemove: string[] = [];
    this.clientToApiKey.forEach((clientApiKey, clientId) => {
      if (clientApiKey === apiKey) {
        clientsToRemove.push(clientId);
      }
    });

    clientsToRemove.forEach(clientId => {
      this.removeClientById(clientId);
    });
  }

  static removeClientById(clientId: string) {
    const ws = this.activeClients.get(clientId);
    if (ws) {
      ws.terminate();
      this.activeClients.delete(clientId);
      this.clientToApiKey.delete(clientId);
      this.clientHeartbeats.delete(clientId);
      console.log(`Removed client ${clientId}`);
    }
  }

  // Check if a client is connected by clientId
  static isClientConnected(clientId: string): boolean {
    const ws = this.activeClients.get(clientId);
    return ws !== undefined && ws.readyState === WebSocket.OPEN;
  }

  // Check if any client is connected for an API key
  static isApiKeyConnected(apiKey: string): boolean {
    for (const [clientId, clientApiKey] of this.clientToApiKey.entries()) {
      if (clientApiKey === apiKey && this.isClientConnected(clientId)) {
        return true;
      }
    }
    return false;
  }

  // Get client WebSocket connection by clientId
  static getClientConnection(clientId: string): WebSocket | undefined {
    return this.activeClients.get(clientId);
  }

  // Get client WebSocket connection by API key (returns first found)
  static getClientConnectionByApiKey(apiKey: string): WebSocket | undefined {
    for (const [clientId, clientApiKey] of this.clientToApiKey.entries()) {
      if (clientApiKey === apiKey) {
        const ws = this.activeClients.get(clientId);
        if (ws && ws.readyState === WebSocket.OPEN) {
          return ws;
        }
      }
    }
    return undefined;
  }

  static async executeTest(apiKey: string, testData: any, clientId?: string): Promise<void> {
    // Use clientId if provided, otherwise fall back to apiKey for backward compatibility
    const connectionId = clientId || apiKey;
    let ws = this.activeClients.get(connectionId);

    // Always run the test, regardless of WebSocket connection state
    if (!ws || ws.readyState !== WebSocket.OPEN) {
      console.log(`⚠️ No active WebSocket connection for ${connectionId}, running test in background mode`);
      ws = undefined; // Ensure we handle the undefined case properly
    } else {
      console.log(`✅ Active WebSocket connection found for ${connectionId}, running test with real-time updates`);
    }

    // Return a Promise that resolves only when the test execution is complete
    return new Promise<void>((resolve, reject) => {
      this.executeTestInternal(apiKey, testData, ws, resolve, reject, clientId);
    });
  }

  private static async executeTestInternal(
    apiKey: string,
    testData: any,
    ws: WebSocket | undefined,
    resolve: () => void,
    reject: (error: Error) => void,
    clientId?: string
  ): Promise<void> {

    // Try to validate user JWT token with backend, fall back to JWT_SECRET if not provided
    const userJwtToken = testData.userJwtToken;
    let authToken: string;

    if (userJwtToken) {
      console.log('Validating user JWT token with backend...');
      const isAuthenticated = await this.validateUserToken(userJwtToken);
      if (isAuthenticated) {
        console.log('User authentication successful, using user JWT token');
        authToken = userJwtToken;
      } else {
        console.log('User JWT token validation failed, falling back to JWT_SECRET');
        authToken = this.generateServiceToken();
      }
    } else {
      console.log('No user JWT token provided, using JWT_SECRET fallback');
      authToken = this.generateServiceToken();
    }

    // Check if there's a recent test run that needs cleanup time
    const lastRunTime = this.lastTestRunTimes.get(apiKey);
    const now = Date.now();
    if (lastRunTime && (now - lastRunTime < 5000)) {
      // If less than 5 seconds since last test, wait a bit
      const waitTime = 5000 - (now - lastRunTime);
      console.log(`Recent test detected, waiting ${waitTime}ms for cleanup...`);
      await sleep(waitTime);
    }

    try {
      // Record this test run time
      this.lastTestRunTimes.set(apiKey, Date.now());

      // Initialize logs collection for this test
      this.testLogs.set(apiKey, []);

      // Send test start notification (if WebSocket is available)
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'test_start',
          testCaseId: testData.testCaseId,
          tcId: testData.tcId,
          jobId: clientId // Include client ID for message filtering
        }));
      } else {
        console.log('📤 Test start notification (no WebSocket connection)');
      }

      // Use existing master.spec.ts file instead of generating a new one
      const testFilePath = path.join(process.cwd(), 'tests', 'master.spec.ts');
      console.log(`Test data for: ${testData.testCase.title}`);
      console.log(`Using existing test file: ${testFilePath}`);

      // Generate JWT token for AgentQ library authentication
      const agentqJwtToken = authToken; // Use the same token we validated earlier

      // Configure dynamic test environment for SaaS
      TestEnvironmentService.setTestEnvironment({
        ...testData,
        clientId,
        apiKey
      });

      // Set environment variables for the test
      const env = {
        ...process.env,
        TEST_DATA: JSON.stringify(testData),
        AGENTQ_API_KEY: apiKey,
        AGENTQ_TOKEN: apiKey, // AgentQ library looks for this variable
        AGENTQ_JWT_TOKEN: agentqJwtToken, // Pass JWT token to AgentQ library
        CLIENT_ID: clientId, // Pass unique client ID to prevent artifact data mixing
        TEST_CASE_ID: testData.testCaseId // Pass test case ID as additional identifier
      };

      // Get dynamic test command based on environment
      const command = TestEnvironmentService.getTestCommand(testFilePath);
      console.log(`Executing test`);

      // Store detailed error information
      let detailedError = '';

      const childProcess = exec(command, { env }, async (error, stdout, stderr) => {
        // Clean up process reference
        this.activeProcesses.delete(apiKey);

        // Get collected logs
        const logs = this.testLogs.get(apiKey) || [];

        // Determine if this is a test execution error vs test assertion failure
        const isTestAssertionFailure = error && (
          error.message.includes('ExpectError') ||
          error.message.includes('Timed out') ||
          error.message.includes('expect(') ||
          stderr?.includes('ExpectError') ||
          stderr?.includes('Timed out') ||
          stderr?.includes('expect(')
        );

        // Job execution status vs test result status
        const jobExecutedSuccessfully = !error || isTestAssertionFailure;
        const testStatus = error && !isTestAssertionFailure ? 'failed' :
                          error && isTestAssertionFailure ? 'failed' : 'passed';

        console.log(`🔍 Test execution analysis:`, {
          hasError: !!error,
          isTestAssertionFailure,
          jobExecutedSuccessfully,
          testStatus
        });

        // If there was an error, capture the detailed error message
        if (error && stderr) {
          detailedError = stderr;
          // Add the detailed error to logs if it's not already there
          if (!logs.some(log => log.includes(stderr.substring(0, 100)))) {
            logs.push(`⚠️ ${stderr}`);
          }
        }

        // Save test results to backend using authenticated token
        try {
          await this.saveTestResults(testData, logs, testStatus, authToken, detailedError);
        } catch (saveError) {
          console.error('Failed to save test results:', saveError);
        }

        try {
          await this.uploadTestVideo(testData, authToken);
        } catch (videoError) {
          console.error('Failed to upload test video:', videoError);
        }

        // Clean up logs from memory
        this.testLogs.delete(apiKey);

        // Send WebSocket notification based on test result (not job execution status)
        if (testStatus === 'failed') {
          console.log(`Test assertions failed (job executed successfully): ${error?.message || 'Unknown test failure'}`);
          if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'test_complete',
              status: 'failed',
              error: error?.message || 'Test assertions failed',
              stdout,
              stderr,
              jobId: clientId // Include client ID for message filtering
            }));
          } else {
            console.log('📤 Test completion notification (test failed, no WebSocket connection)');
          }
        } else {
          console.log('Test execution and assertions completed successfully');
          if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'test_complete',
              status: 'passed',
              stdout,
              stderr,
              jobId: clientId // Include client ID for message filtering
            }));
          } else {
            console.log('📤 Test completion notification (test passed, no WebSocket connection)');
          }
        }

        // BullMQ job resolution based on job execution success (not test result)
        if (jobExecutedSuccessfully) {
          console.log(`✅ Test job completed successfully for API key: ${apiKey}, client: ${clientId || 'unknown'}`);
          resolve(); // Job executed successfully regardless of test outcome
        } else {
          console.error(`❌ Test job execution failed for API key: ${apiKey}, client: ${clientId || 'unknown'}:`, error);
          reject(new Error(error?.message || 'Test execution failed')); // Only reject for actual execution failures
        }

        // Set the test as inactive
        this.activeTests.set(apiKey, false);

        // Close the connection after sending the completion message
        console.log(`Test execution completed for client ${apiKey}, closing connection`);

        // Add a small delay before closing to ensure the message is sent
        setTimeout(() => {
          if (ws && ws.readyState === WebSocket.OPEN) {
            console.log(`Closing connection for client ${apiKey}`);
            ws.close(1000, "Test execution completed");
          }
        }, 1000);
      });

      // Set the test as active
      this.activeTests.set(apiKey, true);

      // Store the process reference
      this.activeProcesses.set(apiKey, childProcess);

      // Stream output in real-time
      childProcess.stdout?.on('data', (data) => {
        const output = data.toString();

        // Split output by newlines and process each line separately
        // This prevents multi-line chunks from being filtered as a whole
        const lines = output.split('\n');

        for (const line of lines) {
          if (!line.trim()) continue; // Skip empty lines

          const formattedOutput = formatTestOutput(line);

          if (formattedOutput) {
            // Check if this exact message was already sent
            if (!this.sentLogMessages.has(formattedOutput)) {
              console.log('Test output:', formattedOutput);

              // Add to logs collection
              const logs = this.testLogs.get(apiKey) || [];
              logs.push(formattedOutput);
              this.testLogs.set(apiKey, logs);

              if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                  type: 'test_output',
                  output: formattedOutput,
                  jobId: clientId // Include client ID for message filtering
                }));
              }

              // Add to the set of sent messages
              this.sentLogMessages.add(formattedOutput);
            }
          }
        }
      });

      childProcess.stderr?.on('data', (data) => {
        const output = data.toString();

        // Split output by newlines and process each line separately
        const lines = output.split('\n');

        for (const line of lines) {
          if (!line.trim()) continue; // Skip empty lines

          const formattedOutput = formatTestOutput(line);

          if (formattedOutput) {
            // Check if this exact message was already sent
            if (!this.sentLogMessages.has(formattedOutput)) {
              console.log('Test error:', formattedOutput);

              // Add to logs collection
              const logs = this.testLogs.get(apiKey) || [];
              logs.push(`ERROR: ${formattedOutput}`);
              this.testLogs.set(apiKey, logs);

              if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                  type: 'test_output',
                  output: formattedOutput,
                  isError: true,
                  jobId: clientId // Include client ID for message filtering
                }));
              }

              // Add to the set of sent messages
              this.sentLogMessages.add(formattedOutput);
            }
          }
        }
      });

      // Clear the sent messages set when the test completes
      childProcess.on('close', async (code) => {
        this.sentLogMessages.clear();
        this.activeTests.set(apiKey, false);

        // Note: Test completion and Promise resolution is handled in the exec callback above
        // This is just for cleanup
        console.log(`Child process closed with code ${code} for client ${apiKey}`);
      });

    } catch (error) {
      console.error('Error in executeTest:', error);
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'test_error',
          message: error instanceof Error ? error.message : 'Failed to execute test',
          jobId: clientId // Include client ID for message filtering
        }));
      } else {
        console.log('📤 Test error notification (no WebSocket connection)');
      }

      // Reject the Promise for BullMQ
      reject(error instanceof Error ? error : new Error('Failed to execute test'));
    }
  }



  static handleClientDisconnect(apiKey: string): void {
    // Check if there's an active test for this client
    if (this.activeTests.get(apiKey)) {
      console.log(`Client ${apiKey} disconnected but test is still running. Allowing test to complete.`);
      // Don't kill the process, let it complete
    } else {
      // If no active test or test already completed, clean up
      const process = this.activeProcesses.get(apiKey);
      if (process) {
        console.log(`Cleaning up process for disconnected client ${apiKey}`);
        process.kill();
        this.activeProcesses.delete(apiKey);
      }
    }
    
    // Make sure to clean up any other resources
    this.sentLogMessages.clear();
    this.activeTests.set(apiKey, false);
  }

  static canRunTest(apiKey: string): boolean {
    // Check if there's already an active test for this client
    if (this.activeTests.get(apiKey)) {
      return false;
    }

    // Check if there's a process still running for this client
    if (this.activeProcesses.has(apiKey)) {
      return false;
    }

    return true;
  }

  // Generate service JWT token using JWT_SECRET
  private static generateServiceToken(): string {
    const jwtSecret = process.env.JWT_SECRET || 'your_jwt_secret_key_change_in_production';
    const jwt = require('jsonwebtoken');
    return jwt.sign(
      {
        sub: 'test-runner-service',
        role: 'service'
      },
      jwtSecret,
      { expiresIn: '1h' }
    );
  }

  // Validate user JWT token with backend
  private static async validateUserToken(userJwtToken: string): Promise<boolean> {
    try {
      const backendUrl = process.env.AGENTQ_API_URL || 'http://localhost:3010';

      // Try to validate the token by calling the profile endpoint
      const response = await axios.get(`${backendUrl}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${userJwtToken}`
        },
        timeout: 5000
      });

      if (response.status === 200 && response.data) {
        console.log('User JWT token validation successful:', response.data.email || 'Unknown user');
        return true;
      }

      return false;
    } catch (error) {
      console.error('User JWT token validation failed:', error);

      // If profile endpoint doesn't exist, try login verification endpoint
      try {
        const backendUrl = process.env.AGENTQ_API_URL || 'http://localhost:3010';
        const verifyResponse = await axios.post(`${backendUrl}/auth/verify`, {}, {
          headers: {
            'Authorization': `Bearer ${userJwtToken}`
          },
          timeout: 5000
        });

        if (verifyResponse.status === 200) {
          console.log('User JWT token verification successful');
          return true;
        }
      } catch (verifyError) {
        console.error('User JWT token verification also failed:', verifyError);
      }

      return false;
    }
  }

  // Save test results to backend API
  private static async saveTestResults(
    testData: any,
    logs: string[],
    status: string,
    authToken: string,
    detailedError: string = ''
  ): Promise<void> {
    try {
      const backendUrl = process.env.AGENTQ_API_URL || 'http://localhost:3010';

      // Use the provided auth token for backend authentication
      console.log('Using auth token for backend authentication');
      const token = authToken;

      // Helper function to validate UUID format
      const isValidUUID = (uuid: string): boolean => {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidRegex.test(uuid);
      };

      // Extract test duration from logs
      let duration: number | null = null;
      for (const log of logs) {
        // Look for patterns like "✅ 1 passed (3.3s)" or "Test completed in 5.2s" or "❌ 1 failed (2.1s)"
        const durationMatch = log.match(/(?:passed|completed|finished|failed|took)\s*\(?([\d.]+)s\)?/i);
        if (durationMatch && durationMatch[1]) {
          // Convert to milliseconds and store
          duration = Math.round(parseFloat(durationMatch[1]) * 1000);
          console.log(`Extracted test duration: ${duration}ms from log: "${log}"`);
          break;
        }
      }

      // Debug: Log the received test data structure
      console.log('Received testData structure:', {
        testCaseId: testData.testCaseId,
        tcId: testData.tcId,
        projectId: testData.projectId,
        testCaseProjectId: testData.testCase?.projectId,
        hasTestCase: !!testData.testCase,
        testCaseKeys: testData.testCase ? Object.keys(testData.testCase) : [],
        duration: duration
      });

      // Ensure we have valid UUIDs
      const testCaseId = testData.testCaseId && isValidUUID(testData.testCaseId)
        ? testData.testCaseId
        : "00000000-0000-0000-0000-000000000001";

      // Try to get projectId from multiple sources
      let projectId = "00000000-0000-0000-0000-000000000000";

      // First check if projectId is directly available
      if (testData.projectId && isValidUUID(testData.projectId)) {
        projectId = testData.projectId;
      } 
      // Then check if it's in the testCase object
      else if (testData.testCase?.projectId && isValidUUID(testData.testCase.projectId)) {
        projectId = testData.testCase.projectId;
      }
      // If still not found, try to fetch it from the API using the testCaseId
      else if (testCaseId && testCaseId !== "00000000-0000-0000-0000-000000000001") {
        try {
          // Make a request to get the test case details
          const response = await axios.get(`${backendUrl}/test-cases/${testCaseId}`, {
            headers: {
              'Authorization': `Bearer ${token}`
            },
            timeout: 5000
          });
          
          if (response.data && response.data.projectId && isValidUUID(response.data.projectId)) {
            projectId = response.data.projectId;
            console.log(`Retrieved projectId ${projectId} from API for testCaseId ${testCaseId}`);
          }
        } catch (error) {
          console.error(`Failed to fetch projectId for testCaseId ${testCaseId}:`, error);
        }
      }

      console.log('Using projectId:', projectId, 'from:', {
        direct: testData.projectId,
        fromTestCase: testData.testCase?.projectId,
        fromAPI: projectId !== testData.projectId && projectId !== testData.testCase?.projectId ? projectId : undefined
      });

      // Extract error message if test failed
      let errorMessage = null;
      if (status === 'failed') {
        // First use the detailed error if available
        if (detailedError) {
          // Function to strip ANSI color codes
          const stripAnsi = (str: string) => {
            return str.replace(/[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g, '');
          };
          
          // Clean the detailed error
          const cleanedError = stripAnsi(detailedError);
          
          // Look for the most important parts of the error using regex
          const timedOutMatch = cleanedError.match(/Timed out .+expect\(.+\)\.toHaveText\(.+\)([\s\S]*?)Expected string: "([^"]+)"([\s\S]*?)Received string: "([^"]+)"/);
          if (timedOutMatch) {
            errorMessage = `Test failed: Timed out waiting for element to have text.\nExpected: "${timedOutMatch[2]}"\nActual: "${timedOutMatch[4]}"`;
          } else {
            // If no specific pattern match, extract the most useful lines
            const errorLines = cleanedError.split('\n').filter(line => 
              !line.includes('at /Users/') && 
              !line.includes('node:internal/') &&
              !line.includes('at Object.<anonymous>') &&
              !line.includes('at processTicksAndRejections') &&
              !line.includes('matcherResult:') &&
              !line.includes('Symbol(step)') &&
              !line.includes('stepId:') &&
              !line.includes('function:') &&
              !line.includes('steps:') &&
              !line.trim().startsWith('    at ') &&
              !line.trim().startsWith('{') &&
              !line.trim().startsWith('}') &&
              !line.trim().startsWith('[') &&
              !line.trim().startsWith(']') &&
              line.trim() !== ''
            );
            
            // Join the filtered lines to create a clean error message
            const filteredError = errorLines.join('\n').trim();
            
            // Extract just the core error message
            const coreErrorMatch = filteredError.match(/Error: (.*?)(?:Call log:|$)/s);
            if (coreErrorMatch) {
              errorMessage = coreErrorMatch[1].trim();
            } else {
              errorMessage = filteredError;
            }
          }
          
          // If the error message is too long, truncate it
          if (errorMessage && errorMessage.length > 2000) {
            errorMessage = errorMessage.substring(0, 1997) + '...';
          }
          
          console.log('Cleaned error message:', errorMessage);
        }
        
        // If no detailed error, look for error in logs (existing code)
        if (!errorMessage) {
          // First look for detailed error messages with "Timed out" or "ExpectError"
          for (const log of logs) {
            if ((log.includes('Timed out') && log.includes('expect(')) || 
                log.includes('ExpectError:') || 
                (log.includes('Error:') && log.includes('Expected') && log.includes('Received'))) {
              
              console.log('Found detailed error message in logs:', log);
              
              // Strip ANSI color codes
              const stripAnsi = (str: string) => {
                return str.replace(/[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g, '');
              };
              
              errorMessage = stripAnsi(log);
              break;
            }
          }
          
          // If still no detailed error, look for any error message
          if (!errorMessage) {
            for (const log of logs) {
              if (log.includes('Error:') || log.includes('❌')) {
                errorMessage = log;
                break;
              }
            }
          }
        }
      }

      // Create a more minimal payload with only essential fields
      const testResultData = {
        testCaseId: testCaseId,
        tcId: testData.tcId || "TC-UNKNOWN",
        projectId: projectId,
        status: status,
        logs: logs,
        duration: duration, // Include the extracted duration
        errorMessage: errorMessage, // Include error message if test failed
        // Send a simplified version of test data to reduce payload size
        testData: {
          title: testData.testCase?.title || "Unknown Test",
          steps: testData.steps?.length || 0
        },
        executedAt: new Date().toISOString(),
        summary: `Test ${status} with ${logs.length} log entries${duration ? ` in ${duration}ms` : ''}`
      };

      console.log(`Saving test results to ${backendUrl}/temp-test-results with JWT auth`);
      
      await axios.post(`${backendUrl}/temp-test-results`, testResultData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000, // 10 second timeout
      });

      console.log(`Test results saved to backend for test case: ${testData.testCaseId}`);
    } catch (error) {
      console.error('Failed to save test results to backend:', error);
      
      // Log more detailed error information
      if (axios.isAxiosError(error) && error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
    }
  }

  // Upload test video to backend
  private static async uploadTestVideo(testData: any, authToken: string): Promise<void> {
    try {
      const fs = require('fs');
      const path = require('path');
      const FormData = require('form-data');

      const testCaseId = testData.testCaseId || 'unknown';

      // Primary: Look for video files in session-based directory (new structure)
      const sessionVideoDir = path.join(process.cwd(), 'client-apps', `session-${testCaseId}`, 'video');

      // Fallback: Look for video files in test-results directory (legacy structure)
      const testResultsDir = path.join(process.cwd(), 'test-results');

      // Check session-based directory first (new structure)
      if (fs.existsSync(sessionVideoDir)) {
        console.log(`📹 Looking for video in session directory: ${sessionVideoDir}`);
      } else if (fs.existsSync(testResultsDir)) {
        console.log(`📹 Looking for video in legacy test-results directory: ${testResultsDir}`);
      } else {
        console.log('⚠️ No video directory found (checked session and test-results), skipping video upload');
        return;
      }

      // Find video files using unique client ID to prevent data mixing
      const clientId = authToken; // Use authToken as fallback if clientId not available

      // Try multiple possible video paths with unique identifiers
      // Prioritize session-based structure, then fall back to legacy
      const possibleVideoPaths = [
        // Primary: Session-based structure (NEW)
        path.join(sessionVideoDir, 'session-video.mp4'),
        path.join(sessionVideoDir, 'session-video.webm'),
        // Secondary: Legacy test-results structure
        path.join(testResultsDir, clientId, 'video.webm'),
        path.join(testResultsDir, `TestCase-${testCaseId}`, 'video.webm'),
        path.join(testResultsDir, `Test-${clientId}`, 'video.webm'),
        // Tertiary: Legacy fallback patterns (for backward compatibility)
        path.join(testResultsDir, `master-${testData.tcId}`, 'video.webm'),
        path.join(testResultsDir, `master-test`, 'video.webm')
      ];

      // Search for video files with priority for unique client ID directories
      const findVideoFiles = (dir: string, clientId: string): string[] => {
        const videoFiles: string[] = [];
        try {
          if (!fs.existsSync(dir)) {
            return videoFiles;
          }

          const items = fs.readdirSync(dir);
          for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            if (stat.isDirectory()) {
              // Prioritize directories that match our client ID or test case ID
              if (item === clientId || item.includes(clientId) || item.includes(testCaseId)) {
                videoFiles.push(...findVideoFiles(fullPath, clientId));
              } else {
                // Still search other directories but with lower priority
                videoFiles.push(...findVideoFiles(fullPath, clientId));
              }
            } else if (item.endsWith('.webm') || item.endsWith('.mp4')) {
              // Support both webm and mp4 formats
              videoFiles.push(fullPath);
            }
          }
        } catch (error) {
          console.error('Error reading directory:', error);
        }
        return videoFiles;
      };

      // Get all video files from both session and legacy directories
      let allVideoFiles: string[] = [];

      // First, check session-based directory (highest priority)
      if (fs.existsSync(sessionVideoDir)) {
        allVideoFiles.push(...findVideoFiles(sessionVideoDir, clientId));
      }

      // Then, check legacy test-results directory
      if (fs.existsSync(testResultsDir)) {
        allVideoFiles.push(...findVideoFiles(testResultsDir, clientId));
      }

      // Add the specific paths we're looking for, prioritizing unique identifier paths
      const videoFiles = [...possibleVideoPaths.filter(p => fs.existsSync(p)), ...allVideoFiles];

      if (videoFiles.length === 0) {
        console.log('⚠️ No video files found for upload');
        return;
      }

      console.log(`📹 Found ${videoFiles.length} video file(s) for potential upload`);

      // Prioritize video files from session directory, then client directory, then by most recent
      const videoFile = videoFiles.sort((a, b) => {
        // Highest priority: files in session-based directory (new structure)
        const aInSessionDir = a.includes(`session-${testCaseId}`) && a.includes('video');
        const bInSessionDir = b.includes(`session-${testCaseId}`) && b.includes('video');

        if (aInSessionDir && !bInSessionDir) return -1;
        if (!aInSessionDir && bInSessionDir) return 1;

        // Second priority: files in client-specific directories (legacy)
        const aInClientDir = a.includes(clientId) || a.includes(testCaseId);
        const bInClientDir = b.includes(clientId) || b.includes(testCaseId);

        if (aInClientDir && !bInClientDir) return -1;
        if (!aInClientDir && bInClientDir) return 1;

        // Third priority: most recent file
        const statA = fs.statSync(a);
        const statB = fs.statSync(b);
        return statB.mtime.getTime() - statA.mtime.getTime();
      })[0];

      console.log(`📹 Found video file for upload: ${videoFile}`);

      // First, get the test result ID from the backend
      const backendUrl = process.env.AGENTQ_API_URL || 'http://localhost:3010';

      // Use the provided auth token for backend authentication
      console.log('🔐 Using auth token for video upload authentication');
      const token = authToken;

      // Get the test result by testCaseId to find the result ID
      const testResultResponse = await axios.get(
        `${backendUrl}/temp-test-results/test-case/${testData.testCaseId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          },
          timeout: 5000
        }
      );

      if (!testResultResponse.data || testResultResponse.data.length === 0) {
        console.log('⚠️ No test result found for video upload');
        return;
      }

      // Get the most recent test result
      const testResult = testResultResponse.data[0];
      const testResultId = testResult.id;

      console.log(`📤 Uploading video for test result ID: ${testResultId}`);

      // Read the video file
      const videoBuffer = fs.readFileSync(videoFile);

      // Determine file extension and content type
      const fileExtension = videoFile.endsWith('.mp4') ? '.mp4' : '.webm';
      const contentType = videoFile.endsWith('.mp4') ? 'video/mp4' : 'video/webm';
      const filename = `video${fileExtension}`;

      // Create form data for video upload
      const formData = new FormData();
      formData.append('file', videoBuffer, {
        filename: filename,
        contentType: contentType
      });

      // Upload video to backend
      await axios.post(
        `${backendUrl}/temp-test-results/${testResultId}/video`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'Authorization': `Bearer ${token}`
          },
          timeout: 30000, // 30 second timeout for video upload
          maxContentLength: 100 * 1024 * 1024, // 100MB max
          maxBodyLength: 100 * 1024 * 1024
        }
      );

      console.log(`✅ Video uploaded successfully for test result: ${testResultId}`);

      // Clean up the video file after successful upload
      try {
        fs.unlinkSync(videoFile);
        console.log(`🗑️ Cleaned up video file: ${videoFile}`);

        // Also clean up the video directory if it becomes empty
        const videoDir = path.dirname(videoFile);

        // For session-based structure, clean up video/ folder if empty
        if (videoDir.includes(`session-${testCaseId}`) && videoDir.endsWith('video')) {
          try {
            const remainingFiles = fs.readdirSync(videoDir);
            if (remainingFiles.length === 0) {
              fs.rmdirSync(videoDir);
              console.log(`🗑️ Cleaned up empty video directory: ${videoDir}`);
            } else {
              console.log(`📁 Video directory not empty, keeping: ${videoDir}`);
            }
          } catch (dirCleanupError) {
            console.log('⚠️ Video directory not empty or already cleaned up');
          }
        }
        // For legacy structure, clean up client-specific directory if empty
        else if (videoDir.includes(clientId) || videoDir.includes(testCaseId)) {
          try {
            const remainingFiles = fs.readdirSync(videoDir);
            if (remainingFiles.length === 0) {
              fs.rmdirSync(videoDir);
              console.log(`🗑️ Cleaned up empty client directory: ${videoDir}`);
            }
          } catch (dirCleanupError) {
            console.log('⚠️ Client directory not empty or already cleaned up');
          }
        }
      } catch (cleanupError) {
        console.error('❌ Failed to clean up video file:', cleanupError);
      }

    } catch (error) {
      console.error('Failed to upload test video:', error);

      // Log more detailed error information
      if (axios.isAxiosError(error) && error.response) {
        console.error('Video upload response status:', error.response.status);
        console.error('Video upload response data:', error.response.data);
      }
    }
  }

  // Add a method to handle test execution with retries
  static async executeTestWithRetries(apiKey: string, testData: any, maxRetries = 3): Promise<void> {
    let retries = 0;
    
    while (retries <= maxRetries) {
      try {
        await this.executeTest(apiKey, testData);
        return; // Success, exit the loop
      } catch (error) {
        retries++;
        
        if (retries > maxRetries) {
          // Max retries reached, rethrow the error
          throw error;
        }
        
        // Wait before retrying
        const delay = 1000 * Math.pow(2, retries - 1); // Exponential backoff
        // Check if it's an app startup error
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('SplashActivity never started')) {
          console.log(`📱 App startup failed - this might be due to Android version compatibility or emulator readiness`);
          console.log(`💡 Suggestion: Check if emulator is fully booted and try a different Android API version`);
        }

        console.log(`Test execution failed, retrying in ${delay}ms (${retries}/${maxRetries})`);
        console.log(`Error details: ${errorMessage}`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
}

// Initialize the heartbeat checker
TestRunnerService.startHeartbeatChecker();
