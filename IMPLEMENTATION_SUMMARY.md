# Device Farm Video Download - Implementation Summary

## Overview

Implemented automatic video download from Device Farm after test execution. Videos are now stored in session-specific folders under `client-apps/session-{testCaseId}/video/`.

## What Was Implemented

### 1. Device Farm Video Service (`src/services/device-farm-video.ts`)

A new service that handles video downloads from Device Farm with the following features:

- **Multiple endpoint attempts**: Tries different API endpoints to download videos
- **Fallback to local storage**: Searches common Device Farm storage locations if API fails
- **Session details retrieval**: Can fetch session information from Device Farm
- **Build and session-based download**: Supports downloading by build ID and session ID
- **Video availability waiting**: Can wait for Device Farm to finish processing videos

**Key Methods:**
- `downloadSessionVideo(sessionId, testCaseId)` - Main download method
- `findLocalDeviceFarmVideo(sessionId, targetDir)` - Fallback local search
- `getSessionDetails(sessionId)` - Get session info from Device Farm
- `downloadVideoByBuildAndSession(buildId, sessionId, testCaseId)` - Download using build/session IDs
- `waitForVideoAvailability(sessionId, maxWaitSeconds)` - Wait for video processing

### 2. Test Runner Integration (`src/services/test-runner.ts`)

Added video download to the test completion flow:

- **Import**: Added `DeviceFarmVideoService` import
- **New method**: `downloadDeviceFarmVideo(testData, clientId)` - Downloads video after test
- **Integration**: Called after `uploadTestVideo()` in test completion callback
- **Session ID retrieval**: Reads session ID from environment variable or `session-info.json` file
- **Error handling**: Gracefully handles failures without breaking test execution

### 3. Session ID Capture (`wdio.conf.ts`)

Modified the WebDriverIO `before` hook to capture session information:

- **Session ID capture**: Stores `browser.sessionId` when test starts
- **Environment variable**: Sets `APPIUM_SESSION_ID` for easy access
- **Persistent storage**: Creates `session-info.json` file with session details
- **Session folder creation**: Ensures session directory exists

**Session Info Structure:**
```json
{
  "sessionId": "ca861231-b15b-4eb4-b548-6a92a69c6ffb",
  "testCaseId": "41d09021-1a6c-4a13-8a1a-edee6554ba97",
  "timestamp": "2025-01-04T10:30:45.123Z"
}
```

### 4. Documentation (`docs/DEVICE_FARM_VIDEO_DOWNLOAD.md`)

Comprehensive documentation covering:
- How the feature works
- Directory structure
- Configuration requirements
- Troubleshooting guide
- Example logs
- Future enhancements

## Directory Structure

```
client-apps/
└── session-{testCaseId}/
    ├── {app-files}              # Downloaded app files
    ├── session-info.json        # Session metadata (NEW)
    └── video/                   # Video folder (NEW)
        └── session-video.mp4    # Downloaded video (NEW)
```

## Flow Diagram

```
Test Execution Start
        ↓
[wdio.conf.ts - before hook]
  - Capture browser.sessionId
  - Save to APPIUM_SESSION_ID env var
  - Create session-info.json
        ↓
Test Steps Execute
        ↓
Test Completion
        ↓
[test-runner.ts - executeTestInternal]
  - Save test results
  - Upload test video (existing)
  - Download Device Farm video (NEW)
        ↓
[device-farm-video.ts - downloadSessionVideo]
  - Read session ID from env or file
  - Wait 5 seconds for processing
  - Try multiple API endpoints
  - Fallback to local storage search
  - Save video to session/video/ folder
        ↓
Video Available in Session Folder
```

## Configuration Required

### Environment Variables (.env)

```env
DEVICE_FARM_HOSTNAME=localhost
DEVICE_FARM_PORT=4723
DEVICE_FARM_ACCESS_KEY=agentq_4dZFoEnNB2IvX
DEVICE_FARM_TOKEN=39a3d273-bdf4-4e2b-b443-42c5fd805a0a

# Device Farm Dashboard Authentication (Required for video download)
DEFAULT_ADMIN_USERNAME=agentq
DEFAULT_ADMIN_PASSWORD=agentq
```

### Device Farm Capabilities

Video recording must be enabled:

```javascript
{
  'df:recordVideo': true,
  'df:options': {
    saveDeviceLogs: true,
    build: `test_${Date.now()}`,
    recordVideo: true
  }
}
```

## API Endpoints Used

The service uses the Device Farm Dashboard API to get session information and download videos:

### 1. Get Session Info
```
GET http://localhost:4723/device-farm/api/dashboard/session
Authentication: Basic Auth (username: agentq, password: agentq)
```

Response includes:
- `videoRecording`: Path to video file (e.g., `{sessionId}/video/{sessionId}.mp4`)
- `status`: Session status (passed/failed)
- `deviceLogs`: Path to device logs

### 2. Download Video
```
GET http://localhost:4723/device-farm/{videoRecording}
Authentication: Basic Auth (username: agentq, password: agentq)
```

Example: `http://localhost:4723/device-farm/f29a8e6d-7c73-4ea2-bffd-b2b727f363ca/video/f29a8e6d-7c73-4ea2-bffd-b2b727f363ca.mp4`

### Fallback Endpoints

If the API method fails, it tries these endpoints:

1. `http://localhost:4723/device-farm/{sessionId}/video/{sessionId}.mp4`
2. `http://localhost:4723/device-farm/{sessionId}/video/{sessionId}.webm`
3. `http://localhost:4723/device-farm/videos/{sessionId}.mp4`
4. `http://localhost:4723/device-farm/videos/{sessionId}.webm`

If all HTTP endpoints fail, it searches these local paths:

1. `{cwd}/device-farm/videos/{sessionId}.mp4`
2. `{cwd}/device-farm/videos/{sessionId}.webm`
3. `{cwd}/.device-farm/videos/{sessionId}.mp4`
4. `{cwd}/.device-farm/videos/{sessionId}.webm`
5. `~/.appium/device-farm/videos/{sessionId}.mp4`
6. `~/.appium/device-farm/videos/{sessionId}.webm`

## Example Logs

### Successful Execution

```
🔧 Setting up global test environment...
📹 Captured session ID for video download: ca861231-b15b-4eb4-b548-6a92a69c6ffb
💾 Session info saved to: /path/to/client-apps/session-41d09021-1a6c-4a13-8a1a-edee6554ba97/session-info.json
...
Test execution and assertions completed successfully
📹 Attempting to download Device Farm video for test case: 41d09021-1a6c-4a13-8a1a-edee6554ba97
📄 Session ID loaded from file: ca861231-b15b-4eb4-b548-6a92a69c6ffb
🔍 Using session ID: ca861231-b15b-4eb4-b548-6a92a69c6ffb
⏳ Waiting 5 seconds for Device Farm to process video...
🔍 Trying endpoint: http://localhost:4723/device-farm/api/session/ca861231-b15b-4eb4-b548-6a92a69c6ffb/video
✅ Video downloaded successfully: /path/to/client-apps/session-41d09021-1a6c-4a13-8a1a-edee6554ba97/video/session-video.mp4
📊 Video size: 2.45 MB
✅ Device Farm video downloaded successfully
```

## Files Modified

1. **src/services/device-farm-video.ts** (NEW)
   - Complete video download service implementation

2. **src/services/test-runner.ts** (MODIFIED)
   - Added import for `DeviceFarmVideoService`
   - Added `downloadDeviceFarmVideo()` method
   - Integrated video download in test completion flow

3. **wdio.conf.ts** (MODIFIED)
   - Enhanced `before` hook to capture session ID
   - Creates `session-info.json` file
   - **Updated video reporter configuration with dynamic outputDir**
   - Videos now saved to `client-apps/session-{testCaseId}/video/`

4. **docs/DEVICE_FARM_VIDEO_DOWNLOAD.md** (NEW)
   - Comprehensive documentation

5. **docs/VIDEO_RECORDING_SETUP.md** (NEW)
   - Video recording configuration and architecture
   - Session-based folder structure documentation

6. **IMPLEMENTATION_SUMMARY.md** (NEW)
   - This file

## Testing the Implementation

### 1. Run a Test

```bash
npm run dev
# or
npm start
```

### 2. Check Session Folder

After test completion, check:

```bash
ls -la client-apps/session-{testCaseId}/
# Should show:
# - session-info.json
# - video/ directory

ls -la client-apps/session-{testCaseId}/video/
# Should show:
# - session-video.mp4 (or .webm)
```

### 3. Verify Session Info

```bash
cat client-apps/session-{testCaseId}/session-info.json
# Should show session ID and metadata
```

### 4. Check Logs

Look for these log messages:
- `📹 Captured session ID for video download`
- `💾 Session info saved to`
- `📹 Attempting to download Device Farm video`
- `✅ Video downloaded successfully`

## Troubleshooting

### Video Not Downloaded

**Check:**
1. Device Farm is running: `http://localhost:4723/device-farm/`
2. Video recording is enabled in capabilities
3. Session ID was captured (check `session-info.json`)
4. Device Farm dashboard shows video: `http://localhost:4723/device-farm/#/builds?buildId=xxx/session/xxx`

**Solutions:**
- Increase wait time in `downloadDeviceFarmVideo()` (currently 5 seconds)
- Check Device Farm logs for errors
- Verify authentication credentials in `.env`
- Try accessing video manually via Device Farm dashboard

### Session ID Not Captured

**Check:**
1. `wdio.conf.ts` `before` hook is executing
2. `browser.sessionId` is available
3. Session folder has write permissions

**Solutions:**
- Add more logging in `before` hook
- Check WebDriverIO session initialization
- Verify file system permissions

## Future Enhancements

1. **Retry logic**: Retry download if video not ready
2. **Build ID extraction**: Get build ID from Device Farm for better API calls
3. **Video metadata**: Extract duration, resolution, etc.
4. **Cloud upload**: Automatically upload to GCS/S3
5. **Video compression**: Reduce file size
6. **Thumbnails**: Generate preview images
7. **Cleanup**: Auto-delete old videos after upload

## Notes

- Video download is **non-blocking** - test results are saved even if video download fails
- Videos are stored **locally** in session folders for easy access
- Session folders are **persistent** - not automatically cleaned up
- Video format depends on Device Farm configuration (usually MP4 or WebM)
- Download waits **5 seconds** for Device Farm to process video

## Related URLs

- Device Farm Dashboard: `http://localhost:4723/device-farm/`
- Session Example: `http://localhost:4723/device-farm/#/builds?buildId=4e7f79b4-4094-406a-aa63-5986a3acfa12/session/ca861231-b15b-4eb4-b548-6a92a69c6ffb`
- Device Farm Docs: https://devicefarm.org

## Support

For issues or questions:
1. Check `docs/DEVICE_FARM_VIDEO_DOWNLOAD.md`
2. Review session folder for `session-info.json`
3. Check Device Farm dashboard for video availability
4. Review test execution logs

